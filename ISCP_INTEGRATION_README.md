# 安全交互平台集成说明

## 概述

本项目已完成安全交互平台的集成改造，移除了原有的 `mobile-igw-func` 依赖，改用安全交互平台进行网络通信。

## 改造内容

### 1. 新增文件

- `utils/iscpConfig.js` - 安全交互平台配置文件
- `utils/iscprh.js` - 安全交互平台核心功能模块
- `utils/sm.js` - 加密解密模块（简化版本）
- `utils/iscpTest.js` - 安全交互平台测试工具

### 2. 修改文件

- `main.js` - 移除 mobile-igw-func 初始化，添加安全交互平台初始化
- `utils/request.js` - 完全重写网络请求模块，集成安全交互平台
- `package.json` - 添加 md5 依赖

### 3. 移除依赖

- 不再使用 `mobile-igw-func`
- 不再使用 `window.Api.request`

## 配置说明

### 环境配置

在 `utils/iscpConfig.js` 中配置：

```javascript
export const appConfig = {
    isProd: isProd, // 自动根据 NODE_ENV 判断
    agentid: isProd ? '1001249' : '1012389', // 微信应用ID
    isRh: true, // 使用新版安全交互平台
    isEncrypt: true, // 是否加密
    iscprh: {
        gateway: [...], // 网关配置
        business: [...] // 业务配置
    }
}
```

### UAT环境测试配置

客户提供的UAT环境配置：
```json
{
    "agentId": "1012389",
    "appName": "供电服务指挥测试1",
    "container": "供电服务指挥测试1",
    "index": "index.html",
    "openType": "1",
    "showAppBar": false
}
```

## 工作原理

1. **安全隧道建立**：通过微信企业号的安全交互平台建立加密隧道
2. **本地代理**：安全交互平台在本地创建代理端口（如 127.0.0.1:18001）
3. **请求转发**：所有API请求先发到本地代理，再通过安全隧道转发到真实服务器

## 使用方法

### 基本请求

```javascript
import request from '@/utils/request'

// 原有的请求方式保持不变
const response = await request({
    url: '/api/test',
    method: 'POST',
    data: { key: 'value' }
})
```

### 测试安全交互平台

```javascript
import { runFullIscpTest, showTestResults } from '@/utils/iscpTest'

// 运行完整测试
const results = await runFullIscpTest()
showTestResults(results)
```

## 核心API

### iscpRhConfig()
初始化安全交互平台配置

### iscpRhConnectCtrl(gatewayId, connCtrlType)
控制安全交互平台连接
- `gatewayId`: 网关ID
- `connCtrlType`: 1=建立连接, 2=断开连接

### iscpRhConnState(gatewayId)
查询连接状态
- 返回值: 0=未连接, 1=连接中, 2=已连接, -1=查询失败

### iscpRhLocalPortCtrl(gatewayId, aclId)
获取本地代理端口

### iscpRhUrl(gatewayId, aclId, url)
获取完整的代理URL

## 注意事项

1. **wx对象依赖**：安全交互平台需要微信企业号环境，依赖 `wx.invoke` 方法
2. **环境区分**：生产环境和测试环境使用不同的网关ID和配置
3. **错误处理**：网络请求失败时会自动重试建立连接
4. **加密支持**：支持请求数据的加密传输（需要完善 sm.js 中的加密算法）

## 测试步骤

1. 确保在微信企业号环境中运行
2. 检查 wx 对象是否可用
3. 运行安全交互平台测试：
   ```javascript
   import { runFullIscpTest } from '@/utils/iscpTest'
   runFullIscpTest()
   ```
4. 测试实际API请求是否正常

## 故障排查

### 常见问题

1. **wx对象未定义**：确保在微信企业号环境中运行
2. **连接失败**：检查网关配置和网络连接
3. **端口获取失败**：确保安全交互平台已正确初始化
4. **请求超时**：检查目标服务器是否可达

### 调试方法

1. 查看控制台日志
2. 使用测试工具验证各个功能模块
3. 检查网络请求的实际URL

## 后续工作

1. **完善加密模块**：根据实际需求实现 `utils/sm.js` 中的加密算法
2. **错误处理优化**：完善各种异常情况的处理
3. **性能优化**：优化连接复用和缓存机制
4. **监控告警**：添加安全交互平台状态监控
