import Vue from "vue";
import App from "./App";
import store from "./store"; // store
import plugins from "./plugins"; // plugins
import "./permission"; // permission
import Vant from "vant";
import "vant/lib/index.less";
import VmFormRender from "./static/codepass-VmFormRender.umd.min.js"; //引入VFormRender组件
import "./static/codepass-VmFormRender.css"; //引入VFormRender样式
import { iscpRhConfig } from "./utils/iscprh"; // 引入安全交互平台
App.mpType = "app";

// 注意：原有的mobile-igw-func配置已移除，现在使用安全交互平台配置

Vue.config.productionTip = false;
Vue.prototype.$store = store;
Vue.use(plugins);
Vue.use(Vant); // 全局注册 Vant
Vue.use(VmFormRender); // 全局注册 VFormRender 等组件

// 检查 wx 对象是否就绪
function checkWxReady(maxAttempts = 10, maxRefreshAttempts = 3) {
  return new Promise((resolve, reject) => {
    let attempts = 0;
    let refreshAttempts = 0;

    const check = () => {
      if (typeof wx !== "undefined" && wx.config) {
        resolve();
      } else if (attempts >= maxAttempts) {
        if (refreshAttempts < maxRefreshAttempts) {
          console.warn(`wx 对象初始化超时，尝试刷新页面 (${refreshAttempts + 1}/${maxRefreshAttempts})`);
          refreshAttempts++;
          attempts = 0; // 重置尝试计数
          location.reload(); // 刷新页面
        } else {
          const error = new Error("wx 对象初始化超时，已达到最大刷新次数");
          console.error(error);
          reject(error);
        }
      } else {
        attempts++;
        setTimeout(check, 100);
      }
    };
    check();
  });
}

// 初始化安全交互平台
checkWxReady()
  .then(async () => {
    try {
      // 初始化安全交互平台配置
      await iscpRhConfig();
      console.log("安全交互平台初始化成功");
    } catch (error) {
      console.error("安全交互平台初始化失败:", error);
    }
  })
  .catch((error) => {
    console.error("无法初始化安全交互平台，wx 对象加载失败:", error);
  });

// 写一个全局 this.$message
Vue.prototype.$message = (msg) => {
  // 用 vant 的 message 组件
  Vue.prototype.$toast(msg);
};
// 开发环境下启用 vconsole
const VConsole = require("vconsole");
new VConsole();

const app = new Vue({
  ...App,
});

app.$mount();
