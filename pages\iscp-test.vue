<template>
  <div class="iscp-test-page">
    <van-nav-bar title="安全交互平台测试" left-arrow @click-left="goBack" />
    
    <div class="test-container">
      <van-cell-group title="测试功能">
        <van-cell title="配置初始化测试" :value="testResults.config" @click="testConfig" />
        <van-cell title="连接状态查询" :value="testResults.state" @click="testConnState" />
        <van-cell title="建立连接测试" :value="testResults.connect" @click="testConnect" />
        <van-cell title="获取本地端口" :value="testResults.port" @click="testLocalPort" />
        <van-cell title="获取代理URL" :value="testResults.url" @click="testUrl" />
      </van-cell-group>
      
      <van-cell-group title="综合测试">
        <van-cell title="运行完整测试" @click="runFullTest" />
        <van-cell title="测试API请求" @click="testApiRequest" />
      </van-cell-group>
      
      <van-cell-group title="环境信息">
        <van-cell title="当前环境" :value="envInfo.env" />
        <van-cell title="网关ID" :value="envInfo.gatewayId" />
        <van-cell title="应用ID" :value="envInfo.appId" />
        <van-cell title="wx对象状态" :value="envInfo.wxStatus" />
      </van-cell-group>
    </div>
    
    <div class="test-logs">
      <van-cell-group title="测试日志">
        <div class="log-content">
          <div v-for="(log, index) in logs" :key="index" class="log-item">
            <span class="log-time">{{ log.time }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </van-cell-group>
    </div>
  </div>
</template>

<script>
import { 
  testIscpConfig, 
  testIscpConnState, 
  testIscpConnect, 
  testIscpLocalPort, 
  testIscpUrl, 
  runFullIscpTest 
} from '@/utils/iscpTest'
import { appConfig } from '@/utils/iscpConfig'
import request from '@/utils/request'

export default {
  name: 'IscpTest',
  data() {
    return {
      testResults: {
        config: '未测试',
        state: '未测试',
        connect: '未测试',
        port: '未测试',
        url: '未测试'
      },
      envInfo: {
        env: process.env.NODE_ENV,
        gatewayId: appConfig.isProd ? 18002 : 18001,
        appId: appConfig.isProd ? 'GDFWZH' : 'GDFWZHCS',
        wxStatus: typeof wx !== 'undefined' ? '可用' : '不可用'
      },
      logs: []
    }
  },
  methods: {
    goBack() {
      uni.navigateBack()
    },
    
    addLog(message) {
      const time = new Date().toLocaleTimeString()
      this.logs.unshift({ time, message })
      if (this.logs.length > 50) {
        this.logs.pop()
      }
    },
    
    async testConfig() {
      this.addLog('开始测试配置初始化...')
      try {
        const result = await testIscpConfig()
        this.testResults.config = result ? '✅ 成功' : '❌ 失败'
        this.addLog(`配置初始化测试: ${this.testResults.config}`)
      } catch (error) {
        this.testResults.config = '❌ 异常'
        this.addLog(`配置初始化测试异常: ${error.message}`)
      }
    },
    
    async testConnState() {
      this.addLog('开始测试连接状态查询...')
      try {
        const state = await testIscpConnState()
        const stateText = state === '2' ? '已连接' : state === '1' ? '连接中' : '未连接'
        this.testResults.state = `${stateText} (${state})`
        this.addLog(`连接状态查询: ${this.testResults.state}`)
      } catch (error) {
        this.testResults.state = '❌ 异常'
        this.addLog(`连接状态查询异常: ${error.message}`)
      }
    },
    
    async testConnect() {
      this.addLog('开始测试建立连接...')
      try {
        const result = await testIscpConnect()
        this.testResults.connect = result ? '✅ 成功' : '❌ 失败'
        this.addLog(`建立连接测试: ${this.testResults.connect}`)
      } catch (error) {
        this.testResults.connect = '❌ 异常'
        this.addLog(`建立连接测试异常: ${error.message}`)
      }
    },
    
    async testLocalPort() {
      this.addLog('开始测试获取本地端口...')
      try {
        const port = await testIscpLocalPort()
        this.testResults.port = port ? `✅ ${port}` : '❌ 失败'
        this.addLog(`获取本地端口: ${this.testResults.port}`)
      } catch (error) {
        this.testResults.port = '❌ 异常'
        this.addLog(`获取本地端口异常: ${error.message}`)
      }
    },
    
    async testUrl() {
      this.addLog('开始测试获取代理URL...')
      try {
        const url = await testIscpUrl('/api/test')
        this.testResults.url = url ? `✅ ${url}` : '❌ 失败'
        this.addLog(`获取代理URL: ${this.testResults.url}`)
      } catch (error) {
        this.testResults.url = '❌ 异常'
        this.addLog(`获取代理URL异常: ${error.message}`)
      }
    },
    
    async runFullTest() {
      this.addLog('开始运行完整测试...')
      try {
        const results = await runFullIscpTest()
        
        this.testResults.config = results.config ? '✅ 成功' : '❌ 失败'
        this.testResults.connect = results.connect ? '✅ 成功' : '❌ 失败'
        this.testResults.state = results.state === '2' ? '✅ 已连接' : results.state === '1' ? '🔄 连接中' : '❌ 未连接'
        this.testResults.port = results.port ? `✅ ${results.port}` : '❌ 失败'
        this.testResults.url = results.url ? `✅ ${results.url}` : '❌ 失败'
        
        this.addLog('完整测试完成')
        
        uni.showModal({
          title: '测试完成',
          content: '完整测试已完成，请查看测试结果',
          showCancel: false
        })
      } catch (error) {
        this.addLog(`完整测试异常: ${error.message}`)
      }
    },
    
    async testApiRequest() {
      this.addLog('开始测试API请求...')
      try {
        // 测试一个简单的API请求
        const response = await request({
          url: '/h5/getInfo',
          method: 'POST',
          data: {}
        })
        
        this.addLog(`API请求测试成功: ${JSON.stringify(response)}`)
        uni.showToast({
          title: 'API请求成功',
          icon: 'success'
        })
      } catch (error) {
        this.addLog(`API请求测试失败: ${error.message}`)
        uni.showToast({
          title: 'API请求失败',
          icon: 'error'
        })
      }
    }
  },
  
  onLoad() {
    this.addLog('安全交互平台测试页面加载完成')
    this.addLog(`当前环境: ${this.envInfo.env}`)
    this.addLog(`网关ID: ${this.envInfo.gatewayId}`)
    this.addLog(`wx对象状态: ${this.envInfo.wxStatus}`)
  }
}
</script>

<style scoped>
.iscp-test-page {
  background-color: #f7f8fa;
  min-height: 100vh;
}

.test-container {
  padding: 16px;
}

.test-logs {
  margin-top: 16px;
  padding: 0 16px;
}

.log-content {
  max-height: 300px;
  overflow-y: auto;
  background-color: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
}

.log-item {
  display: block;
  margin-bottom: 4px;
  font-size: 12px;
  line-height: 1.4;
}

.log-time {
  color: #666;
  margin-right: 8px;
}

.log-message {
  color: #333;
}
</style>
