// 安全交互平台配置文件
// 基于客户提供的示例进行适配

// 判断是否为生产环境
const isProd = process.env.NODE_ENV === 'production'

// 安全交互平台配置
export const appConfig = {
    isProd: isProd, // 是否是生产环境
    /**------------------------------配置参数----------------------------------**/
    agentid: isProd ? '1001249' : '1012389', // agentid 应用微信id
    isRh: true, // 是否使用新版安全交互平台
    isDebug: false, // 是否调试模式
    isEncrypt: true, // 是否加密
    
    // 安全交互平台配置
    iscprh: {
        gateway: [
            {
                acl_id_list: [1],
                gateway_id: isProd ? 18002 : 18001,
                appId: isProd ? 'GDFWZH' : 'GDFWZHCS',
                sagIP: '**************',
                sagPort: isProd ? '20084' : '20084'
            }
        ],
        business: [
            {
                acl_id: 1,
                gateway_id: isProd ? 18002 : 18001,
                ip: isProd ? '**************' : '*************',
                port: isProd ? '18002' : '18001'
            }
        ]
    },
    
    // 调试配置（如果需要）
    debugUrl: '', // 调试环境URL
}
