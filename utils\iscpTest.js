// 安全交互平台测试工具
// 用于验证安全交互平台功能是否正常

import { iscpRhConfig, iscpRhConnState, iscpRhConnectCtrl, iscpRhLocalPortCtrl, iscpRhUrl } from './iscprh'
import { appConfig } from './iscpConfig'

/**
 * 测试安全交互平台配置
 */
export async function testIscpConfig() {
    try {
        console.log('开始测试安全交互平台配置...');
        const result = await iscpRhConfig();
        console.log('安全交互平台配置测试结果:', result);
        return result === 1;
    } catch (error) {
        console.error('安全交互平台配置测试失败:', error);
        return false;
    }
}

/**
 * 测试安全交互平台连接状态
 */
export async function testIscpConnState() {
    try {
        console.log('开始测试安全交互平台连接状态...');
        const gatewayId = appConfig.isProd ? 18002 : 18001;
        const state = await iscpRhConnState(gatewayId);
        console.log('安全交互平台连接状态:', state);
        return state;
    } catch (error) {
        console.error('安全交互平台连接状态测试失败:', error);
        return '-1';
    }
}

/**
 * 测试安全交互平台连接控制
 */
export async function testIscpConnect() {
    try {
        console.log('开始测试安全交互平台连接控制...');
        const gatewayId = appConfig.isProd ? 18002 : 18001;
        const result = await iscpRhConnectCtrl(gatewayId, 1); // 1表示建立连接
        console.log('安全交互平台连接控制测试结果:', result);
        return result === 1;
    } catch (error) {
        console.error('安全交互平台连接控制测试失败:', error);
        return false;
    }
}

/**
 * 测试获取本地端口
 */
export async function testIscpLocalPort() {
    try {
        console.log('开始测试获取本地端口...');
        const gatewayId = appConfig.isProd ? 18002 : 18001;
        const port = await iscpRhLocalPortCtrl(gatewayId, 1);
        console.log('获取本地端口测试结果:', port);
        return port !== '-1' ? port : null;
    } catch (error) {
        console.error('获取本地端口测试失败:', error);
        return null;
    }
}

/**
 * 测试获取安全交互平台URL
 */
export async function testIscpUrl(testUrl = '/test') {
    try {
        console.log('开始测试获取安全交互平台URL...');
        const gatewayId = appConfig.isProd ? 18002 : 18001;
        const url = await iscpRhUrl(gatewayId, 1, testUrl);
        console.log('获取安全交互平台URL测试结果:', url);
        return url;
    } catch (error) {
        console.error('获取安全交互平台URL测试失败:', error);
        return '';
    }
}

/**
 * 完整的安全交互平台功能测试
 */
export async function runFullIscpTest() {
    console.log('=== 开始完整的安全交互平台功能测试 ===');
    
    const results = {
        config: false,
        connect: false,
        state: '-1',
        port: null,
        url: ''
    };
    
    try {
        // 1. 测试配置
        results.config = await testIscpConfig();
        
        // 2. 测试连接
        if (results.config) {
            results.connect = await testIscpConnect();
        }
        
        // 3. 测试状态
        results.state = await testIscpConnState();
        
        // 4. 测试端口
        if (results.state === '1' || results.state === '2') {
            results.port = await testIscpLocalPort();
        }
        
        // 5. 测试URL
        results.url = await testIscpUrl('/api/test');
        
        console.log('=== 安全交互平台功能测试完成 ===');
        console.log('测试结果:', results);
        
        return results;
    } catch (error) {
        console.error('安全交互平台功能测试异常:', error);
        return results;
    }
}

/**
 * 在页面中显示测试结果
 */
export function showTestResults(results) {
    const status = {
        config: results.config ? '✅ 成功' : '❌ 失败',
        connect: results.connect ? '✅ 成功' : '❌ 失败',
        state: results.state === '2' ? '✅ 已连接' : results.state === '1' ? '🔄 连接中' : '❌ 未连接',
        port: results.port ? `✅ ${results.port}` : '❌ 获取失败',
        url: results.url ? `✅ ${results.url}` : '❌ 获取失败'
    };
    
    const message = `安全交互平台测试结果：
配置初始化: ${status.config}
连接控制: ${status.connect}
连接状态: ${status.state}
本地端口: ${status.port}
代理URL: ${status.url}`;
    
    console.log(message);
    
    // 在uni-app中显示结果
    if (typeof uni !== 'undefined') {
        uni.showModal({
            title: '安全交互平台测试',
            content: message,
            showCancel: false
        });
    }
    
    return message;
}
