// 安全交互平台核心功能模块
// 基于客户提供的示例进行适配

import { appConfig } from './iscpConfig';

/**
 * 安全交互平台配置初始化
 * @returns {Promise} 返回配置结果
 */
export function iscpRhConfig() {
    var businessServiceInfoArr = [];
    var gatewayServiceInfoArr = [];

    var iscprh = appConfig.iscprh;

    // 处理网关配置
    var gateway = iscprh.gateway;
    gateway.forEach(config => {
        var acl_id_list = config.acl_id_list
        acl_id_list.forEach((item, index, arr) => { arr[index] = parseInt(item) });

        gatewayServiceInfoArr.push({
            gatewayInfo: {
                ip: config.sagIP,
                port: parseInt(config.sagPort),
                resource: {
                    acl_id_list: acl_id_list,
                    appId: config.appId
                },
                type: 2
            },
            gateway_id: parseInt(config.gateway_id)
        })
    })

    // 处理业务配置
    var business = iscprh.business;
    business.forEach(config => {
        businessServiceInfoArr.push({
            gateway_id: parseInt(config.gateway_id),
            acl_id: parseInt(config.acl_id),
            businessInfo: {
                sip: config.ip,
                sport: parseInt(config.port)
            }
        })
    })

    // SSL配置
    var sslProfileObj = {
        ssl_pool: 5,
        ssl_renegotiate: 60,
        ssl_timeout: 60
    }

    return new Promise((resolve, reject) => {
        wx.invoke(
            "ext_ISCP_RhConfig", {
                data: {
                    businessServiceInfoList: businessServiceInfoArr,
                    gatewayServiceInfoList: gatewayServiceInfoArr,
                    sslProfile: sslProfileObj
                }
            },
            function (res) {
                if (res.err_msg === 'ext_ISCP_RhConfig:ok') {
                    // 成功处理
                    console.log('安全交互平台配置成功');
                    resolve(1)
                } else {
                    console.error('安全交互平台配置失败:', res);
                    reject(-1)
                }
            }
        );
    })
}

/**
 * 安全交互平台连接控制
 * @param {number} gatewayServiceId 网关id 配置接口中配置过的网关id
 * @param {number} connCtrlType connCtrlType 1 建立链接  2  断开链接
 * @returns {Promise} 返回连接结果
 */
export function iscpRhConnectCtrl(gatewayServiceId, connCtrlType) {
    return new Promise((resolve, reject) => {
        wx.invoke(
            "ext_ISCP_RhConnectCtrl", {
                data: {
                    gatewayServiceId: parseInt(gatewayServiceId),
                    connCtrlType: connCtrlType + ""
                }
            },
            function (res) {
                if (res.err_msg === 'ext_ISCP_RhConnectCtrl:ok') {
                    // 成功处理
                    console.log('安全交互平台连接控制成功');
                    resolve(1)
                } else {
                    console.error('安全交互平台连接控制失败:', res);
                    reject(-1)
                    if (connCtrlType == 1) {
                        uni.showToast({
                            title: '安全交互平台连接失败',
                            icon: 'none'
                        });
                    }
                }
            }
        );
    })
}

/**
 * 查询安全交互平台连接状态
 * @param {number} gatewayServiceId 网关服务ID
 * @returns {Promise} 返回连接状态
 * 值	说明	备注
 * 0	未连接
 * 1	正在连接
 * 2	已连接
 * -1 查询失败
 */
export function iscpRhConnState(gatewayServiceId) {
    return new Promise((resolve, reject) => {
        wx.invoke("ext_ISCP_RhConnState", {
            data: {
                gatewayServiceId: parseInt(gatewayServiceId)
            }
        }, function (res) {
            // 安全交互平台状态查询成功
            if (res.err_msg === 'ext_ISCP_RhConnState:ok') {
                console.log('安全交互平台状态查询成功:', res.result);
                resolve(res.result + '')
            } else {
                console.error('安全交互平台状态查询失败:', res);
                reject('-1')
            }
        })
    })
}

/**
 * 获取本地端口
 * @param {number} gatewayServiceId 网关服务ID
 * @param {number} aclId ACL ID
 * @returns {Promise} 返回本地端口
 */
export function iscpRhLocalPortCtrl(gatewayServiceId, aclId) {
    return new Promise((resolve, reject) => {
        wx.invoke("ext_ISCP_RhLocalPortCtrl", {
            data: {
                gatewayServiceId: parseInt(gatewayServiceId),
                aclId: parseInt(aclId)
            }
        }, function (res) {
            // 安全交互平台状态查询成功
            if (res.err_msg === 'ext_ISCP_RhLocalPortCtrl:ok') {
                var result = res.result;
                if (result.indexOf(':') > 0) {
                    result = result.split(':')[1]
                }
                console.log('获取本地端口成功:', result);
                resolve(result)
            } else {
                console.error('获取本地端口失败:', res);
                reject('-1')
            }
        })
    })
}

/**
 * 释放安全交互平台资源
 * @param {number} gatewayServiceId 网关服务ID
 */
export function iscpRhRelease(gatewayServiceId) {
    wx.invoke("ext_ISCP_RhRelease", {
        data: {
            gatewayServiceId: parseInt(gatewayServiceId)
        }
    }, function (res) {
        console.log('ext_ISCP_RhRelease', res);
    })
}

/**
 * 获取安全交互平台URL
 * @param {number} gatewayServiceId 网关服务ID
 * @param {number} aclId ACL ID
 * @param {string} url 目标URL
 * @returns {Promise<string>} 返回完整的本地代理URL
 */
export async function iscpRhUrl(gatewayServiceId, aclId, url = '') {
    try {
        var state = await iscpRhConnState(gatewayServiceId);
        if (state == '1' || state == '2') {
            var port = await iscpRhLocalPortCtrl(gatewayServiceId, aclId)
            if (port != '-1') {
                return "http://127.0.0.1:" + port + url;
            } else {
                uni.showToast({
                    title: '安全交互平台获取端口失败',
                    icon: 'none'
                });
            }
        } else {
            console.log('需要建立连接');
            var connect = await iscpRhConnectCtrl(gatewayServiceId, 1)
            if (connect == 1) {
                var port = await iscpRhLocalPortCtrl(gatewayServiceId, aclId)
                if (port != '-1') {
                    return "http://127.0.0.1:" + port + url;
                } else {
                    uni.showToast({
                        title: '安全交互平台获取端口失败',
                        icon: 'none'
                    });
                }
            } else {
                uni.showToast({
                    title: '安全交互平台连接失败',
                    icon: 'none'
                });
            }
        }
    } catch (error) {
        console.error('获取安全交互平台URL失败:', error);
        uni.showToast({
            title: '安全交互平台异常',
            icon: 'none'
        });
    }
    return '';
}
