import store from '@/store'
import config from '@/config'
import { getToken } from '@/utils/auth'
import errorCode from '@/utils/errorCode'
import { toast, showConfirm, tansParams } from '@/utils/common'
import { URL_CODE_MAP } from '../static/codepass-urlCode'
import { appConfig } from './iscpConfig'
import { iscpRhConnState, iscpRhConnectCtrl, iscpRhLocalPortCtrl } from './iscprh'
import { encrypt, decrypt, auth } from './sm'

let timeout = 60 * 2 * 1000 // 增加超时时间到2分钟
const baseUrl = config.baseUrl
let requestCount = 0
let httpCount = 0

// 请求封装函数
const send = async (url, params = {}, method = 'POST', headers = { 'Content-Type': 'application/json' }, callback, cache, loading, responseType = 'json') => {
    if (appConfig.isDebug) {
        url = appConfig.debugUrl + url;
    } else {
        if (url.indexOf('https') <= 0) {
            url = await getUrl(url);
        }
    }
    return baseRequest(url, params, method, headers, callback, cache, loading, responseType);
}

// 获取URL（通过安全交互平台）
async function getUrl(url) {
    if (appConfig.isRh) {
        url = await getIscpRhUrl(url);
    }
    return url;
}

// 获取安全交互平台URL
async function getIscpRhUrl(url) {
    try {
        const gatewayId = appConfig.isProd ? 18002 : 18001;
        var status = await iscpRhConnState(gatewayId);
        if (status && (status == '1' || status == '2')) {
            const port = uni.getStorageSync('iscp_port');
            url = await iscpUrl(url, port);
        } else {
            console.log('需要建立安全交互平台连接');
            var connect = await iscpRhConnectCtrl(gatewayId, 1);
            if (connect == 1) {
                url = await iscpUrl(url);
            }
        }
    } catch (error) {
        console.error('获取安全交互平台URL失败:', error);
    }
    return url;
}

// 构建安全交互平台URL
const iscpUrl = async (url, port) => {
    const gatewayId = appConfig.isProd ? 18002 : 18001;
    if (!port) {
        port = await iscpRhLocalPortCtrl(gatewayId, 1);
    }
    if (port !== '-1') {
        uni.setStorageSync('iscp_port', port);
        url = "http://127.0.0.1:" + port + url;
    }
    return url;
}

const defaultRequest = config => {
  let userInfo = JSON.parse(sessionStorage.getItem('getUserInfo'))

  if (process.env.NODE_ENV === 'production') {
    if (config.url === '/h5/login') {
      requestCount++
      console.log(`登录请求次数: ${requestCount}`, {
        time: new Date().toLocaleTimeString(),
        config
      })
    }

    // 调试信息
    console.log('请求配置:', {
      url: config.url,
      providedUrlCode: config.urlCode,
      mappedUrlCode: URL_CODE_MAP[config.url],
      params: config.params,
      data: config.data,
      all: config
    })

    // 使用新的安全交互平台请求方式
    return send(
      config.url,
      config.data,
      config.method || 'POST',
      { 'Content-Type': 'application/json;charset=utf-8' },
      null,
      false,
      true
    );
  }

  // 开发环境处理
  const isToken = (config.headers || {}).isToken === false
  config.header = config.header || {}
  config.header['Content-Type'] = 'application/json'
  config.header['Accept'] = 'application/json'
  if (getToken() && !isToken) {
    config.header['Authorization'] = 'Bearer ' + getToken()
  }

  // 修改参数处理逻辑
  if (config.params || config.url !== '/h5/login') {
    let params = config.params || {}
    let url = config.url + '?' + tansParams(params)
    url = url.slice(0, -1)
    config.url = url
  }

  return new Promise((resolve, reject) => {
    uni.request({
      method: config.method || 'get',
      timeout: config.timeout || timeout,
      url: config.baseUrl || baseUrl + config.url,
      data: config.data,
      header: config.header,
      dataType: 'json'
    }).then(response => {
      let [error, res] = response
      if (error) {
        toast('后端接口连接异常')
        reject(error)
        return
      }
      const code = res.data.code || 200
      const msg = errorCode[code] || res.data.msg || errorCode['default']

      if (code !== 200) {
        const err = new Error(msg)
        err.code = code
        err.response = res.data

        if (code === 401) {
          showConfirm('登录状态已过期，您可以继续留在该页面，或者重新登录?').then(res => {
            if (res.confirm) {
              store.dispatch('LogOut').then(() => {
                uni.reLaunch({ url: '/pages/login' })
              })
            }
          })
          reject(err)
        } else {
          toast(msg)
          reject(err)
        }
        return
      }

      resolve(res.data)
    })
      .catch(error => {
        let { message } = error
        if (message === 'Network Error') {
          message = '后端接口连接异常'
        } else if (message.includes('timeout')) {
          message = '系统接口请求超时'
        } else if (message.includes('Request failed with status code')) {
          message = '系统接口' + message.substr(message.length - 3) + '异常'
        }

        const err = new Error(message)
        err.code = error.code || 500
        err.response = error.response

        toast(message)
        reject(err)
      })
  })
}

// 基础请求函数
const baseRequest = (url, params, method, headers, callback, cache = false, loading = true, responseType) => {
    return new Promise((resolve, reject) => {
        showLoading(loading);

        // 处理请求头
        const requestHeaders = {
            ...headers
        };

        // 添加token
        if (getToken()) {
            requestHeaders.token = getToken();
        }

        // 处理加密
        let requestData = params;
        var needEncrypt = headers.encrypt;
        if (needEncrypt == undefined) {
            needEncrypt = true;
            if (appConfig.isEncrypt && needEncrypt) {
                requestData = encrypt(JSON.stringify(params));
                requestHeaders.auth = auth();
            }
        }

        // 处理表单数据
        const contentType = headers["Content-Type"];
        if (contentType && contentType.indexOf('application/x-www-form-urlencoded') !== -1) {
            requestData = tansParams(requestData);
        }

        uni.request({
            url: url,
            method: method || 'POST',
            data: requestData,
            header: requestHeaders,
            timeout: timeout,
            dataType: 'json',
            responseType: responseType || 'text'
        }).then(response => {
            let [error, res] = response;
            clearLoading(loading);

            if (error) {
                console.error('服务端错误：url：' + url + 'err：' + error);
                toast('网络异常，请确认网络状态');
                reject(error);
                return;
            }

            if (res.statusCode !== 200) {
                const err = new Error('请求失败');
                err.code = res.statusCode;
                toast('获取数据异常');
                reject(err);
                return;
            }

            let result = res.data;

            // 处理解密
            var needDecrypt = requestHeaders.auth;
            if (needDecrypt != undefined && needDecrypt) {
                needDecrypt = true;
            }
            if (appConfig.isEncrypt && needDecrypt && result.secData) {
                var dedata = decrypt(JSON.stringify(result));
                console.log('url:' + url);
                console.log('responseData:' + JSON.stringify(dedata));
                result = dedata.secData;
            }

            // 处理业务错误
            if (result && result.code === '-1004') {
                // 登录过期处理
                uni.removeStorageSync('userInfo');
                uni.removeStorageSync('token');
                uni.removeStorageSync('expire');
                uni.removeStorageSync('code');
                uni.reLaunch({ url: '/pages/login-sso' });
                return;
            }

            showErr(result, url, params);
            resolve({ data: result, status: 200 });

            if (callback) {
                callback(200, { data: result });
            }
        }).catch(err => {
            console.error('服务端错误：url：' + url + 'err：' + err);
            clearLoading(loading);

            const err_msg = err.message || err.errMsg || '网络异常';
            if (err_msg.indexOf('timeout') !== -1) {
                toast('请求超时，请确认网络后再试');
            } else if (err_msg.indexOf('Network') !== -1) {
                toast('网络异常，请确认网络状态');
            } else {
                toast('网络异常，请确认网络状态');
            }

            reject(err);

            if (callback) {
                callback(500, "获取数据异常");
            }
        });
    });
}

// 显示加载中
function showLoading(loading) {
    if (loading) {
        httpCount++;
        uni.showLoading({
            title: '加载中...',
            mask: true
        });
    }
}

// 清除加载中
function clearLoading(loading) {
    if (loading) {
        httpCount--;
        if (httpCount <= 0) {
            httpCount = 0;
            uni.hideLoading();
        }
    }
}

// 显示错误信息
function showErr(res, url, params) {
    if (res && res.status && res.status !== 1000) {
        console.info(url + ':');
        console.debug(res);
        console.info('请求参数：');
        console.debug(params);
    }
}

// 导出请求对象，类似客户示例
export const request = {
    base: (url, params, callback, cache, method = 'POST', loading) => {
        return send(url, params, method, { 'Content-Type': 'application/json;charset=utf-8' }, callback, cache, loading)
    },
    get: (url, params, callback, cache, loading) => {
        return send(url, params, 'GET', { "Content-Type": "application/json;charset=utf-8" }, callback, cache, loading)
    },
    delete: (url, params, callback, cache, loading) => {
        return send(url, params, 'DELETE', { "Content-Type": "application/json;charset=utf-8" }, callback, cache, loading)
    },
    post: (url, params, callback, cache, loading) => {
        return send(url, params, 'POST', { "Content-Type": "application/json;charset=utf-8" }, callback, cache, loading)
    },
    getAudioBlob: (url, params, callback, cache, loading) => {
        return send(url, params, 'GET', { "Content-Type": "application/json;charset=utf-8" }, callback, cache, false, 'blob')
    }
}

// 保持原有的默认导出，确保兼容性
export default defaultRequest
