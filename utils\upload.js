import store from '@/store'
import config from '@/config'
import { getToken } from '@/utils/auth'
import errorCode from '@/utils/errorCode'
import { toast, showConfirm, tansParams } from '@/utils/common'
import { URL_CODE_MAP } from '../static/codepass-urlCode'
import { appConfig } from './iscpConfig'
import { iscpRhConnState, iscpRhConnectCtrl, iscpRhLocalPortCtrl } from './iscprh'

let timeout = 10000
const baseUrl = config.baseUrl

// 获取安全交互平台上传URL
async function getIscpUploadUrl(url) {
    try {
        const gatewayId = appConfig.isProd ? 18002 : 18001;
        var status = await iscpRhConnState(gatewayId);
        if (status && (status == '1' || status == '2')) {
            const port = uni.getStorageSync('iscp_port');
            if (port) {
                return "http://127.0.0.1:" + port + url;
            }
        } else {
            console.log('需要建立安全交互平台连接');
            var connect = await iscpRhConnectCtrl(gatewayId, 1);
            if (connect == 1) {
                const port = await iscpRhLocalPortCtrl(gatewayId, 1);
                if (port !== '-1') {
                    uni.setStorageSync('iscp_port', port);
                    return "http://127.0.0.1:" + port + url;
                }
            }
        }
    } catch (error) {
        console.error('获取安全交互平台上传URL失败:', error);
    }
    return url;
}

const upload = async config => {
  // 生产环境使用安全交互平台
  if (process.env.NODE_ENV === 'production') {
    try {
      // 获取安全交互平台URL
      const uploadUrl = await getIscpUploadUrl(config.url);

      // 添加token到header
      const headers = {
        ...config.header
      };
      if (getToken()) {
        headers.token = getToken();
      }

      return new Promise((resolve, reject) => {
        uni.uploadFile({
          url: uploadUrl,
          filePath: config.filePath,
          name: config.name || 'file',
          header: headers,
          formData: config.formData,
          timeout: timeout,
          success: (res) => {
            let result;
            try {
              result = JSON.parse(res.data);
            } catch (e) {
              console.error('解析上传响应失败:', e);
              reject(new Error('响应数据格式错误'));
              return;
            }

            const code = result.code || 200;
            const msg = errorCode[code] || result.msg || errorCode['default'];

            if (code === 200 || code === 0) {
              resolve(result);
            } else if (code === 401) {
              showConfirm("登录状态已过期，您可以继续留在该页面，或者重新登录?").then(res => {
                if (res.confirm) {
                  store.dispatch('LogOut').then(() => {
                    uni.reLaunch({ url: '/pages/login-sso' });
                  });
                }
              });
              reject(new Error('无效的会话，或者会话已过期，请重新登录。'));
            } else {
              toast(msg);
              reject(new Error(msg));
            }
          },
          fail: (error) => {
            console.error('上传失败:', error);
            let message = error.errMsg || error.message || '上传失败';
            if (message.includes('timeout')) {
              message = '上传超时，请重试';
            } else if (message.includes('Network')) {
              message = '网络异常，请检查网络连接';
            }
            toast(message);
            reject(new Error(message));
          }
        });
      });
    } catch (error) {
      console.error('安全交互平台上传异常:', error);
      return Promise.reject(error);
    }
  }

  // 开发环境保持原有逻辑
  // 是否需要设置 token
  const isToken = (config.headers || {}).isToken === false
  config.header = config.header || {}
  if (getToken() && !isToken) {
    config.header['Authorization'] = 'Bearer ' + getToken()
  }
  // get请求映射params参数
  if (config.params) {
    let url = config.url + '?' + tansParams(config.params)
    url = url.slice(0, -1)
    config.url = url
  }
  return new Promise((resolve, reject) => {
    uni.uploadFile({
      timeout: config.timeout || timeout,
      url: baseUrl + config.url,
      filePath: config.filePath,
      name: config.name || 'file',
      header: config.header,
      formData: config.formData,
      success: (res) => {
        let result = JSON.parse(res.data)
        const code = result.code || 200
        const msg = errorCode[code] || result.msg || errorCode['default']
        if (code === 200) {
          resolve(result)
        } else if (code == 401) {
          showConfirm("登录状态已过期，您可以继续留在该页面，或者重新登录?").then(res => {
            if (res.confirm) {
              store.dispatch('LogOut').then(res => {
                uni.reLaunch({ url: '/pages/login/login' })
              })
            }
          })
          reject('无效的会话，或者会话已过期，请重新登录。')
        } else if (code === 500) {
          toast(msg)
          reject('500')
        } else if (code !== 200) {
          toast(msg)
          reject(code)
        }
      },
      fail: (error) => {
        let { message } = error
        if (message == 'Network Error') {
          message = '后端接口连接异常'
        } else if (message.includes('timeout')) {
          message = '系统接口请求超时'
        } else if (message.includes('Request failed with status code')) {
          message = '系统接口' + message.substr(message.length - 3) + '异常'
        }
        toast(message)
        reject(error)
      }
    })
  })
}

export default upload
